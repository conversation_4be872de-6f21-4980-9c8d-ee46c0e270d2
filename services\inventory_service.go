package services

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"order-center/dto"
	"order-center/models"
	"order-center/utils"
	"os"
	"os/signal"
	"strings"
	"sync"
	"syscall"
	"time"

	"github.com/Shopify/sarama"
	"github.com/go-xorm/xorm"
	"github.com/limitedlee/microservice/common/config"
	"github.com/maybgit/glog"
	"github.com/spf13/cast"
)

var (
	apiDataProducer sarama.AsyncProducer
	kafkaAddress    string
	kafkaAddressArr []string

	//解冻库存url
	inventoryUnfreezeUrl = "/inventory-app/io-bound/unfreeze"
	//退款入库url
	inventoryInBoundUrl = "/inventory-app/io-bound/refund-in"
)

func init() {
	kafkaAddress = config.GetString("kafka.inventory.address")
	env := strings.ToLower(os.Getenv("ASPNETCORE_ENVIRONMENT"))

	//线上的话，用线上已经有的kafka
	if env == "production" || env == "pro" {
		kafkaAddress = config.GetString("kafka.address")
	}
	kafkaAddressArr = strings.Split(kafkaAddress, ",")
	var err error
	apiDataProducer, err = sarama.NewAsyncProducer(kafkaAddressArr, nil)
	if err != nil {
		glog.Error(err)
		//panic(err)
	}
	go DoAllUserDetailKafka()
}

// 同步库存给第三方的结构体
type RetailSkuStock struct {
	// APP方门店id--美团id
	App_poi_code string `json:"app_poi_code" form:"app_poi_code" query:"app_poi_code"`
	// 商品sku库存集合的json数据
	Food_data []FoodData `json:"food_data" form:"food_data" query:"food_data"`
}

// 同步库存给第三方的结构体
type FoodData struct {
	// app_food_code(必填项),APP方商品id
	App_food_code string `json:"app_food_code" form:"app_food_code" query:"app_food_code"`
	// skus(必填项)，商品sku信息集合的json数组
	Skus []Skus `json:"skus" form:"skus" query:"skus"`
}

// 同步库存给第三方的结构体
type Skus struct {
	// sku_id(必填项)，是sku唯一标识码
	Sku_id string `json:"sku_id" form:"sku_id" query:"sku_id"`
	// stock(必填项)，为sku的库存，传非负整数，若传"*"表示库存无限
	Stock string `json:"stock" form:"stock" query:"stock"`
}

// 锁库释放库存的返回结构体
type FreezeResponse struct {
	Code     int    `json:"code"`
	ErrorMsg string `json:"errorMsg"`
	Msg      string `json:"msg"`
	Message  string `json:"message"`
}

type FreezeDetail struct {
	OutCount     int     `json:"outCount"`
	ProductName  string  `json:"productName"`
	ProductPrice float64 `json:"productPrice"`
	SkuId        int64   `json:"skuId"`
	RefDetailId  int64   `json:"refDetailId"`
}

// 锁库和释放库存并且扣减库存的请求参数
type FreezeReq struct {
	// 详情列表
	Details []FreezeDetail `json:"details"`
	// 订单号
	RefId int64 `json:"refId"`
	// 订单号
	RefNo string `json:"refNo"`
	// 出库类型
	RefType string `json:"refType"`
}

type RefundStockDetail struct {
	OutRefDetailId int     `json:"outRefDetailId"`
	ProductName    string  `json:"productName"`
	ProductPrice   float64 `json:"productPrice"`
	RefDetailId    int     `json:"refDetailId"`
	RefundCount    int     `json:"refundCount"`
	SkuId          int64   `json:"skuId"`
}

type InBoundCommand struct {
	ChainId     int64                  `json:"chain_id"`     // 连锁id
	StoreId     string                 `json:"store_id"`     // 店铺id
	WarehouseId int                    `json:"warehouse_id"` // 仓库ID,渠道id不为空时，可以不填
	ChannelId   int                    `json:"channel_id"`   // 渠道ID,仓库id不为空时，可以不填
	RefId       int                    `json:"ref_id"`       // 出库关联单id
	RefNo       string                 `json:"ref_no"`       // 出库关联单单号
	RefType     int                    `json:"ref_type"`     // 库存关联对象类型 1.无，2.采购单，3.订单，4.盘点单，5.其他
	Remark      string                 `json:"remark"`       // 备注
	Details     []InBoundDetailCommand `json:"details"`      // 出库详情
	Operator    string                 `json:"operator"`     // 操作人
}

type InBoundDetailCommand struct {
	ItemDetailRefId string `json:"item_detail_ref_id"` // 出入库关联的单据详情id
	SkuId           int    `json:"sku_id"`             // SKU ID
	ProductName     string `json:"product_name"`       // 商品名称
	IoPrice         int    `json:"io_price"`           // 出库单价，单位为分
	IoCount         int    `json:"io_count"`           // 出库数量
}

// 库存结构体
type VInventory struct {
	SkuId        int64 `json:"sku_id" xorm:"not null comment('sku id') BIGINT 'sku_id'"`
	ProductId    int64 `json:"product_id" xorm:"not null comment('商品id') BIGINT 'product_id'"`
	AvailableNum int   `json:"available_num" xorm:"not null default 0 comment('可用库存') INT 'available_num'"`
	//阿闻SKUID
	AwSkuId int64 `json:"aw_sku_id" xorm:"not null comment('aw_sku_id') BIGINT 'aw_sku_id'"`
}

// 库存结构体
type VIdRelation struct {
	SkuId     int64 `json:"sku_id" xorm:"not null comment('sku id') BIGINT 'sku_id'"`
	ProductId int64 `json:"product_id" xorm:"not null comment('商品id') BIGINT 'product_id'"`
	BaseSkuId int64 `json:"base_sku_id" xorm:"not null comment('base_sku_id') BIGINT 'base_sku_id'"`
}

type UnFreezeReq struct {
	ChainId     int64          `json:"chain_id"`     // 连锁id
	StoreId     string         `json:"store_id"`     // 店铺id
	WarehouseId int            `json:"warehouse_id"` // 仓库ID,渠道id不为空时，可以不填
	RefId       int            `json:"ref_id"`       // 出库关联单id
	RefNo       string         `json:"ref_no"`       // 出库关联单单号
	RefType     int            `json:"ref_type"`     // 库存关联对象类型 1.无，2.采购单，3.订单，4.盘点单，5.其他
	Remark      string         `json:"remark"`       // 备注
	DetailMap   map[string]int `json:"details"`      // 出库详情: key为出入库关联的单据详情id, value为解冻数量
	Operator    string         `json:"operator"`     // 操作人
}

// OutBoundCommand 销售出库
type OutBoundCommand struct {
	ChainId     int64                   `json:"chain_id"`     // 连锁id
	StoreId     string                  `json:"store_id"`     // 店铺id
	WarehouseId int                     `json:"warehouse_id"` // 仓库ID
	ChannelId   int                     `json:"channel_id"`   // 渠道ID
	RefId       int                     `json:"ref_id"`       // 出库关联单id
	RefNo       string                  `json:"ref_no"`       // 出库关联单单号
	RefType     int                     `json:"ref_type"`     // 库存关联对象类型 1.无，2.采购单，3.订单，4.盘点单，5.其他
	Remark      string                  `json:"remark"`       // 备注
	Details     []OutBoundDetailCommand `json:"details"`      // 出库详情
	Operator    string                  `json:"operator"`     // 操作人
}

// OutBoundDetailCommand 出库详情
type OutBoundDetailCommand struct {
	ItemDetailRefId string `json:"item_detail_ref_id"` // 出入库关联的单据详情id
	SkuId           int    `json:"sku_id"`             // SKU ID
	ProductName     string `json:"product_name"`       // 商品名称
	IoPrice         int    `json:"io_price"`           // 出库单价，单位为分
	IoCount         int    `json:"io_count"`           // 出库数量
}

// 对逍宠库存的接口, 锁库，提交订单的时候的锁库
func FreezeCommitOrder(orderSn string, shopId string, db *xorm.Session) (out FreezeResponse) {

	glog.Info("FreezeCommitOrder 参数", orderSn, shopId)

	redisConn := GetRedisConn()
	lockCard := "eshop:lock:FreezeCommitOrder" + orderSn
	lockRes := redisConn.SetNX(lockCard, time.Now().Unix(), 10*time.Second).Val()
	if !lockRes {
		out.Msg = "获取锁失败"
		return
	}
	defer redisConn.Del(lockCard)
	//db := GetDBConn()
	//如果有门店ID直接传，没有的话就查询一下
	var order models.OrderMain
	_, err := db.Table("dc_order.order_main").Where("order_sn=?", orderSn).Get(&order)
	if err != nil {
		out.Msg = "获取订单失败"
		return out
	}

	chainId := ""
	db.Table("datacenter.store").Where("finance_code=?", shopId).Select("chain_id").Get(&chainId)

	//根据订单去查询要锁的库存和数据
	var orderProduct []models.OrderProduct
	db.Where("order_sn=? and product_type=1", orderSn).Find(&orderProduct)
	if len(orderProduct) == 0 || order.OrderType == 22 || order.OrderType == 23 {
		out.Code = 200
		return
	}
	//请求JAVA接口的参数
	Req := OutBoundCommand{
		ChainId:     cast.ToInt64(chainId),
		StoreId:     order.ShopId,
		WarehouseId: cast.ToInt(order.WarehouseId),
		ChannelId:   cast.ToInt(order.ChannelId),
		RefId:       cast.ToInt(order.Id),
		RefNo:       order.OrderSn,
		RefType:     3,
		Remark:      "销售订单出库",
		Details:     make([]OutBoundDetailCommand, 0),
		Operator:    "system",
	}

	// 构建锁库明细
	for _, product := range orderProduct {
		SubBizOrderId := product.SubBizOrderId
		if order.ChannelId == 2 {
			SubBizOrderId = cast.ToString(product.Id)
		}
		Req.Details = append(Req.Details, OutBoundDetailCommand{
			SkuId:           cast.ToInt(product.SkuId),
			ProductName:     product.ProductName,
			IoPrice:         cast.ToInt(product.PayPrice),
			IoCount:         cast.ToInt(product.Number),
			ItemDetailRefId: SubBizOrderId,
		})
	}

	//开始调用接口
	cmd := "/inventory-app/io-bound/freeze"

	dataBytes, err := json.Marshal(Req)
	if err != nil {
		glog.Error("json编码错误err=", err.Error())
		return
	}
	headers := "tenant_id|" + shopId + "&chain_id|" + chainId
	respBody, err := utils.XiaoCHttpPost(cmd, dataBytes, headers)
	glog.Info("锁库参数：", cmd, string(dataBytes), "返回日志:"+string(respBody), err)
	json.Unmarshal(respBody, &out)
	if out.Code != 200 && out.Code != 0 {
		out.Code = 400
		out.Msg = out.Message
		out.ErrorMsg = out.Message
	}
	out.Code = 200
	return
}

// 对逍宠库存的接口,扣减库存并释放锁库
func Freeze(orderSn string, shopId string, refType int) (out FreezeResponse) {
	glog.Info("FreezeCommitOrder 参数", orderSn, shopId)

	//先注释库存操作

	out.Code = 400
	redisConn := GetRedisConn()
	lockCard := "eshop:lock:FreezeCommitOrder" + orderSn
	lockRes := redisConn.SetNX(lockCard, time.Now().Unix(), 10*time.Second).Val()
	if !lockRes {
		out.Msg = "获取锁失败"
		return
	}
	defer redisConn.Del(lockCard)
	db := GetDBConn()
	//如果有门店ID直接传，没有的话就查询一下
	var order models.OrderMain
	_, err := db.Table("order_main").Where("order_sn=?", orderSn).Get(&order)
	if err != nil {
		out.Msg = "获取订单失败"
		return out
	}

	chainId := ""
	db.Table("datacenter.store").Where("finance_code=?", shopId).Select("chain_id").Get(&chainId)

	//根据订单去查询要锁的库存和数据
	var orderProduct []models.OrderProduct
	db.Where("order_sn=? and product_type=1", orderSn).Find(&orderProduct)
	if len(orderProduct) == 0 || order.OrderType == 22 || order.OrderType == 23 {
		out.Code = 200
		return
	}
	//请求JAVA接口的参数
	Req := OutBoundCommand{
		ChainId:     cast.ToInt64(chainId),
		StoreId:     order.ShopId,
		WarehouseId: cast.ToInt(order.WarehouseId),
		ChannelId:   cast.ToInt(order.ChannelId),
		RefId:       cast.ToInt(order.Id),
		RefNo:       order.OrderSn,
		RefType:     3,
		Remark:      "销售订单出库",
		Details:     make([]OutBoundDetailCommand, 0),
		Operator:    "system",
	}

	// 构建锁库明细
	for _, product := range orderProduct {
		SubBizOrderId := product.SubBizOrderId
		if order.ChannelId == 2 {
			SubBizOrderId = cast.ToString(product.Id)
		}
		Req.Details = append(Req.Details, OutBoundDetailCommand{
			SkuId:           cast.ToInt(product.SkuId),
			ProductName:     product.ProductName,
			IoPrice:         cast.ToInt(product.PayPrice),
			IoCount:         cast.ToInt(product.Number),
			ItemDetailRefId: SubBizOrderId,
		})
	}

	//开始调用接口
	cmd := "/inventory-app/io-bound/order-out"

	dataBytes, err := json.Marshal(Req)
	if err != nil {
		glog.Error("json编码错误err=", err)
		return
	}
	headers := "tenant_id|" + shopId + "&chain_id|" + chainId
	respBody, err := utils.XiaoCHttpPost(cmd, dataBytes, headers)
	glog.Info("锁库参数：", cmd, string(dataBytes), "返回日志:"+string(respBody), err)
	json.Unmarshal(respBody, &out)
	if out.Code != 200 && out.Code != 0 {
		out.Code = 400
		out.Msg = out.Message
		out.ErrorMsg = out.Message
	}
	out.Code = 200
	return
}

// 对逍宠库存的接口,锁库和扣减库存接口  refType：1 锁库 ，2扣减库存并释放锁库
func UnFreeze(orderSn string, shopId string) (out FreezeResponse) {
	glog.Info("UnFreeze，释放锁库入参：订单号：", orderSn, "，门店id：", shopId)
	out.Code = 400
	redisConn := GetRedisConn()
	lockCard := "eshop:lock:UnFreeze" + orderSn
	lockRes := redisConn.SetNX(lockCard, time.Now().Unix(), 10*time.Second).Val()
	if !lockRes {
		out.Msg = "获取锁失败"
		return
	}
	defer redisConn.Del(lockCard)

	db := GetDBConn()

	chainId := ""
	db.Table("datacenter.store").Where("finance_code=?", shopId).Select("chain_id").Get(&chainId)

	var order models.OrderMain
	_, err := db.Table("dc_order.order_main").Where("order_sn=?", orderSn).Get(&order)
	if err != nil {
		out.Msg = "获取订单失败"
		return out
	}
	parentOrderSn := order.OrderSn
	// 获取主订单号
	if order.ParentOrderSn != "" {
		parentOrderSn = order.ParentOrderSn
		order = models.OrderMain{}
		_, err = db.Table("dc_order.order_main").Where("order_sn=?", parentOrderSn).Get(&order)
		if err != nil {
			out.Msg = "退货入库查询订单信息失败"
			return
		}
	}

	//根据订单去查询要锁的库存和数据
	var orderProduct []models.OrderProduct
	db.Where("order_sn=? and product_type=1", parentOrderSn).Find(&orderProduct)
	if len(orderProduct) == 0 || order.OrderType == 22 || order.OrderType == 23 {
		out.Code = 200
		return
	}
	//请求JAVA接口的参数
	Req := UnFreezeReq{
		ChainId:     cast.ToInt64(chainId),
		StoreId:     order.ShopId,
		WarehouseId: cast.ToInt(order.WarehouseId),
		RefId:       cast.ToInt(order.Id),
		RefNo:       parentOrderSn,
		RefType:     3,
		Remark:      "销售订单入库",
		Operator:    "system",
		DetailMap:   make(map[string]int),
	}

	for _, product := range orderProduct {
		// 美团订单没有SubBizOrderId，则用行号作为明细id
		SubBizOrderId := cast.ToString(product.SubBizOrderId)
		if order.ChannelId == ChannelMtId {
			SubBizOrderId = cast.ToString(product.Id)
		}
		Req.DetailMap[SubBizOrderId] = cast.ToInt(product.Number)
	}

	dataBytes, err := json.Marshal(Req)
	if err != nil {
		glog.Error("json编码错误err=", err.Error())
		return
	}
	headers := "tenant_id|" + shopId + "&chain_id|" + chainId
	respBody, err := utils.XiaoCHttpPost(inventoryUnfreezeUrl, dataBytes, headers)
	glog.Info("释放锁库参数：", inventoryUnfreezeUrl, string(dataBytes), "返回日志:"+string(respBody), err)
	json.Unmarshal(respBody, &out)
	if out.Code != 200 && out.Code != 0 {
		out.Code = 400
	} else {
		out.Code = 200
	}

	return
}

// 对逍宠库存的接口，退款入库接口
func RefundStock(refundSn string, orderSn string, ChannelId int32) (out FreezeResponse) {
	log := fmt.Sprintf("RefundStock，订单退款退还库存入参：订单号：%s，退款单号：%s", orderSn, refundSn)
	glog.Info(log)

	out.Code = 400
	redisConn := GetRedisConn()
	lockCard := "eshop:lock:RefundStock" + refundSn
	lockRes := redisConn.SetNX(lockCard, time.Now().Unix(), 10*time.Second).Val()
	if !lockRes {
		out.Msg = "获取锁失败"
		return
	}
	defer redisConn.Del(lockCard)
	db := GetDBConn()
	shopId := ""
	type OrderMain struct {
		Id            int64  `json:"id"`
		OrderSn       string `json:"order_sn"`
		ParentOrderSn string `json:"parent_order_sn"`
		ShopId        string `json:"shop_id"`
		WarehouseId   int64  `json:"warehouse_id"`
		ChannelId     int64  `json:"channel_id"`
		OrderType     int    `json:"order_type"`
	}
	order := &OrderMain{}
	if _, err := db.Table("dc_order.order_main").Where("order_sn=?", orderSn).Get(order); err != nil {
		glog.Error(log, ",退货入库 查询订单主信息失败, ", err)
		out.Msg = "退货入库 查询订单信息失败"
		return
	}
	//根据订单去查询要锁的库存和数据
	var par1 []models.OrderProductExd
	switch ChannelId {
	case ChannelAwenId, ChannelIdOfflineShop:
		if order.ParentOrderSn == "" {
			order = &OrderMain{}
			_, err := db.Table("dc_order.order_main").Where("parent_order_sn=?", orderSn).Get(order)
			if err != nil {
				glog.Error(log, ",退货入库 查询订单主信息失败, ", err)
				out.Msg = "退货入库 查询订单信息失败"
				return
			}
		}
		db.SQL(`SELECT parent.*,rop.quantity
		FROM dc_order.order_product child
			INNER JOIN dc_order.order_main om ON child.order_sn = om.order_sn
			INNER JOIN dc_order.order_product parent ON om.parent_order_sn = parent.order_sn AND parent.sku_id = child.sku_id AND parent.discount_price = child.discount_price
			INNER JOIN dc_order.refund_order_product rop ON child.id = rop.order_product_id
		WHERE child.order_sn = ? AND rop.refund_sn = ?`, order.OrderSn, refundSn).Find(&par1)

	case ChannelMtId:

		db.SQL(`SELECT b.*, a.quantity
FROM dc_order.refund_order_product a
     INNER JOIN dc_order.order_product b ON a.sku_id = b.sku_id AND b.discount_price = a.product_price
WHERE a.product_type = 1
  AND a.refund_sn = ?
  AND b.order_sn = ?`, refundSn, order.OrderSn).Find(&par1)

	case ChannelElmId:
		db.SQL(`SELECT b.*, a.quantity
FROM dc_order.refund_order_product a
     INNER JOIN dc_order.order_product b ON a.sku_id = b.sku_id AND b.sub_biz_order_id = a.sub_biz_order_id
WHERE a.product_type = 1
  AND a.refund_sn = ?
  AND b.order_sn = ?`, refundSn, order.OrderSn).Find(&par1)
	}
	if len(par1) == 0 {
		glog.Error(log, ",退货入库查询订单商品信息失败")
		out.Msg = "退货入库查询订单商品信息失败"
		return
	}

	shopId = order.ShopId
	chainId := ""
	db.Table("datacenter.store").Where("finance_code=?", shopId).Select("chain_id").Get(&chainId)
	//请求JAVA接口的参数
	Req := InBoundCommand{
		ChainId:     cast.ToInt64(chainId),
		StoreId:     shopId,
		WarehouseId: cast.ToInt(order.WarehouseId),
		ChannelId:   cast.ToInt(order.ChannelId),
		RefId:       cast.ToInt(order.Id),
		RefNo:       refundSn,
		RefType:     5,
		Remark:      "退款入库",
		Operator:    "system",
	}

	for _, v := range par1 {
		item := InBoundDetailCommand{}
		// 美团订单没有SubBizOrderId，则用行号作为明细id
		SubBizOrderId := cast.ToString(v.SubBizOrderId)
		if ChannelId == ChannelMtId {
			SubBizOrderId = cast.ToString(v.Id)
		}
		item.ItemDetailRefId = SubBizOrderId
		item.SkuId = cast.ToInt(v.SkuId)
		item.ProductName = v.ProductName
		item.IoPrice = cast.ToInt(v.PayPrice)
		item.IoCount = int(v.Quantity)
		Req.Details = append(Req.Details, item)
	}

	//开始调用接口
	cmd := inventoryInBoundUrl
	dataBytes, err := json.Marshal(Req)
	if err != nil {
		glog.Error(log, ",json编码错误err=", err.Error())
		return
	}
	headers := "tenant_id|" + shopId + "&chain_id|" + chainId
	respBody, err := utils.XiaoCHttpPost(cmd, dataBytes, headers)
	glog.Info(log, "退货入库参数：", cmd, string(dataBytes), "返回日志:"+string(respBody), err)
	json.Unmarshal(respBody, &out)
	return
}

func QueryStock(in dto.QueryStockReq) (out dto.QueryStockRes) {
	out = dto.QueryStockRes{
		Code:  400,
		Stock: make(map[int64]dto.VInventory),
	}
	db := GetDBConn()

	var stocks = make([]dto.VInventory, 0)
	session := db.Table("eshop.inventory").Alias("i").
		Select("i.sku_id, i.available_num, l.code as location_code").
		Join("LEFT", "eshop.inventory_location l", "i.store_id = l.store_id AND i.sku_id = l.sku_id").
		Where("i.store_id = ?", in.ShopId)

	if len(in.SkuIds) > 0 {
		session = session.In("i.sku_id", in.SkuIds)
	}
	if len(in.ProductIds) > 0 {
		session = session.In("i.product_id", in.ProductIds)
	}

	err := session.Find(&stocks)
	if err != nil {
		glog.Error("查询库存出错", err.Error())
		out.Message = err.Error()
		return
	}

	if len(stocks) > 0 {
		for _, v := range stocks {
			out.Stock[v.SkuId] = dto.VInventory{
				AvailableNum: v.AvailableNum,
				LocationCode: v.LocationCode,
			}
		}
	}
	out.Code = 200
	return
}

type ConsumerGroupHandler struct {
	wg sync.WaitGroup
}

func (c *ConsumerGroupHandler) Setup(_ sarama.ConsumerGroupSession) error {
	fmt.Println("Consumer group setup")
	return nil
}

func (c *ConsumerGroupHandler) Cleanup(_ sarama.ConsumerGroupSession) error {
	fmt.Println("Consumer group cleanup")
	return nil
}

// 消费kafka
func DoAllUserDetailKafka() {
	// Kafka配置
	config := sarama.NewConfig()
	config.Version = sarama.V2_0_0_0 // 设置Kafka版本，根据实际情况调整
	config.Consumer.Return.Errors = false
	config.Consumer.Offsets.Initial = sarama.OffsetOldest

	// 创建消费者组客户端
	groupID := "inventory_in_out_bound"                                      // 消费者组ID
	topic := "inventory_in_out_bound"                                        // 要消费的主题
	client, err := sarama.NewConsumerGroup(kafkaAddressArr, groupID, config) // 替换为实际的Kafka地址
	if err != nil {
	}
	defer client.Close()

	// 处理退出信号
	signals := make(chan os.Signal, 1)
	signal.Notify(signals, syscall.SIGINT, syscall.SIGTERM)

	// 创建处理器
	handler := &ConsumerGroupHandler{}

	// 开始消费
	ctx, cancel := context.WithCancel(context.Background())
	go func() {
		for {
			err := client.Consume(ctx, []string{topic}, handler)
			if err != nil && err != sarama.ErrClosedClient {
			}
			if ctx.Err() != nil {
				return
			}
		}
	}()

	<-signals         // 等待退出信号
	cancel()          // 取消消费
	handler.wg.Wait() // 等待处理器完成

	fmt.Println("Consumer exiting...")
}

func (c *ConsumerGroupHandler) ConsumeClaim(sess sarama.ConsumerGroupSession, claim sarama.ConsumerGroupClaim) error {
	for msg := range claim.Messages() {
		glog.Info("DoAllUserDetailKafka 接受到数据：" + string(msg.Value))
		//request := string(msg.Value)
		var mode = VInOutBound{}
		json.Unmarshal(msg.Value, &mode)
		//fmt.Println(mode)
		//如果有数据
		//查询出来要变更的库存
		db := GetDBConn()
		if len(mode.Data) > 0 && mode.Data[0].Id != "" {
			item := mode.Data[0]

			// 判断仓库id 查询表dc_dispatch.warehouse_relation_shop是不是渠道2，3的才进行后面的操作
			var channelExists bool
			channelExists, err := db.Table("dc_dispatch.warehouse_relation_shop").
				Where("shop_id = ? AND warehouse_id = ? AND channel_id IN (2,3)",
					item.StoreId,
					cast.ToInt(item.WarehouseId)).Exist()
			if err != nil {
				glog.Error("查询仓库渠道关系出错:", err.Error())
				sess.MarkMessage(msg, "")
				return err
			}

			if !channelExists {
				glog.Info("该仓库未关联美团或饿了么渠道，跳过库存同步, warehouse_id:", item.WarehouseId)
				sess.MarkMessage(msg, "")
				return nil
			}

			var boundDetail = make([]VIoBoundDetail, 0)
			err = db.Table("eshop.inventory_in_out_bound_detail").Where("bound_id=? ", cast.ToInt(item.Id)).Find(&boundDetail)
			if err != nil {
				glog.Error(mode.Data[0].Id, "查询库存出错", err.Error())
				sess.MarkMessage(msg, "") // 标记消息已被处理
				return err
			}
			//如果等于0重复查询几次
			if len(boundDetail) == 0 {
				for i := 0; i < 5; i++ {
					time.Sleep(500 * time.Millisecond)
					err = db.Table("eshop.inventory_in_out_bound_detail").Select("/*FORCE_MASTER*/ *").
						Where("bound_id=? ", cast.ToInt(item.Id)).Find(&boundDetail)
					if len(boundDetail) > 0 {
						break
					}
				}
			}
			if len(boundDetail) == 0 {
				glog.Info("没有查询到数据：", mode.Data[0].Id)
				sess.MarkMessage(msg, "") // 标记消息已被处理
				return err
			}
			glog.Info("库存写入MQ1：", mode.Data[0].Id)
			var ids []int64
			for _, v := range boundDetail {
				ids = append(ids, cast.ToInt64(v.SkuId))
			}
			//查询出我们这边的skuid对应库存 .Select("/*FORCE_MASTER*/ *")
			var stocks = make([]VInventory, 0)
			err = db.Table("eshop.inventory").Alias("i").
				Join("INNER", "eshop.pro_sku p", "i.sku_id = p.id").
				Where("i.store_id = ?", cast.ToInt64(item.StoreId)).
				In("i.sku_id", ids).
				Select("p.product_id, p.id as sku_id, i.available_num").
				Find(&stocks)
			if err != nil {
				glog.Error(mode.Data[0].Id, "查询库存出错", err.Error())
				sess.MarkMessage(msg, "") // 标记消息已被处理
				return err
			}
			glog.Info("库存写入MQ2：", mode.Data[0].Id, stocks)
			if len(stocks) == 0 {
				for i := 0; i < 5; i++ {
					time.Sleep(500 * time.Millisecond)
					err = db.Table("eshop.inventory").Alias("i").
						Join("INNER", "eshop.pro_sku p", "i.sku_id = p.id").
						Where("i.store_id = ?", cast.ToInt64(item.StoreId)).
						In("i.sku_id", ids).
						Select("p.product_id, p.id as sku_id, i.available_num").
						Find(&stocks)
					if len(stocks) > 0 {
						break
					}
				}
			}

			var skuIds []int64
			for _, v := range stocks {
				skuIds = append(skuIds, cast.ToInt64(v.SkuId))
			}
			var data = make([]models.ProProductStoreInfo, 0)
			//查询这些商品在对应的门店是否上架了
			err = db.Table("eshop.pro_product_store_info").Where("store_id=? and channel_id in (2,3) and up_down_state=1", item.StoreId).
				In("sku_id", skuIds).Find(&data)
			if err != nil {
				glog.Error(mode.Data[0].Id, "查询库存出错", err.Error())
				sess.MarkMessage(msg, "") // 标记消息已被处理
				return err
			}
			if len(data) == 0 {
				glog.Info("没有查询到数据：", mode.Data[0].Id)
				sess.MarkMessage(msg, "") // 标记消息已被处理
				return err
			}
			myMap := make(map[string]int)
			for _, v := range data {
				myMap[cast.ToString(v.SkuId)+":"+cast.ToString(v.ChannelId)] = 1
			}
			glog.Info("库存写入MQ3：", mode.Data[0].Id, data, myMap)
			var mqInfos []models.MqInfo
			var foodDataMt []FoodData
			var foodDataELM []FoodData
			for _, v := range stocks {
				if _, exists := myMap[cast.ToString(v.SkuId)+":2"]; exists {
					//美团库存
					foodDataMt = append(foodDataMt, FoodData{
						App_food_code: cast.ToString(v.ProductId),
						Skus: []Skus{{
							Sku_id: cast.ToString(v.SkuId),
							Stock:  cast.ToString(v.AvailableNum),
						}},
					})
				}
				if _, exists := myMap[cast.ToString(v.SkuId)+":3"]; exists {
					//饿了么库存
					foodDataELM = append(foodDataELM, FoodData{
						App_food_code: cast.ToString(v.ProductId),
						Skus: []Skus{{
							Sku_id: cast.ToString(v.SkuId),
							Stock:  cast.ToString(v.AvailableNum),
						}},
					})
				}
			}
			glog.Info("库存写入MQ4：", mode.Data[0].Id, foodDataMt, foodDataELM)
			//美团MQ
			if len(foodDataMt) > 0 {
				contentMt, _ := json.Marshal(RetailSkuStock{
					App_poi_code: item.StoreId,
					Food_data:    foodDataMt,
				})
				mqInfoMt := models.MqInfo{
					Exchange: "datacenter",
					Content:  string(contentMt),
					Quene:    "dc_sz_stock_mq",
				}
				mqInfos = append(mqInfos, mqInfoMt)
			}

			//饿了么MQ
			if len(foodDataELM) > 0 {
				contentMt, _ := json.Marshal(RetailSkuStock{
					App_poi_code: item.StoreId,
					Food_data:    foodDataELM,
				})
				mqInfoMt := models.MqInfo{
					Exchange: "datacenter",
					Content:  string(contentMt),
					Quene:    "dc_sz_stock_mq_elm",
				}
				mqInfos = append(mqInfos, mqInfoMt)
			}
			glog.Info("库存写入MQ5：", foodDataMt, foodDataELM, mqInfos, stocks, myMap, mqInfos)

			if len(mqInfos) > 0 {
				if err = SliceInsertMqInfo(db, mqInfos); err != nil {
					glog.Error("SAAS同步库存到第三方MQ出错：", err.Error())
				}
			}
			//break
		}
		sess.MarkMessage(msg, "") // 标记消息已被处理
	}
	return nil
}

// SliceInsertMqInfo 分段插入
func SliceInsertMqInfo(db *xorm.Engine, data []models.MqInfo) error {
	total := len(data)
	for i := 0; i < total; i = i + 1000 {
		end := i + 1000
		if end > total {
			end = total
		}
		in := data[i:end]
		if _, err := db.Table("dc_order.mq_info").Insert(in); err != nil {
			return errors.New("插入MqInfo " + err.Error())
		}
	}
	return nil
}

type VInOutBound struct {
	Data []struct {
		Id             string `json:"id"`
		ChainId        string `json:"chain_id"`
		StoreId        string `json:"store_id"`
		WarehouseId    string `json:"warehouse_id"`
		BoundNo        string `json:"bound_no"`
		BoundType      string `json:"bound_type"`
		ItemType       string `json:"item_type"`
		ItemRefId      string `json:"item_ref_id"`
		ItemRefNo      string `json:"item_ref_no"`
		ItemRefType    string `json:"item_ref_type"`
		TotalNum       string `json:"total_num"`
		TotalAmount    string `json:"total_amount"`
		SellAmount     string `json:"sell_amount"`
		Remark         string `json:"remark"`
		IsDeleted      string `json:"is_deleted"`
		Operator       string `json:"operator"`
		OccurrenceTime string `json:"occurrence_time"`
		CreatedTime    string `json:"created_time"`
		UpdatedTime    string `json:"updated_time"`
	} `json:"data"`
}

type VIoBoundDetail struct {
	Id        int    `json:"id" xorm:"pk not null comment('主键id') BIGINT 'id'"`
	BoundId   int    `json:"bound_id" xorm:"not null comment('出入库id') BIGINT 'bound_id'"`
	ChainId   int    `json:"chain_id" xorm:"not null comment('连锁id') BIGINT 'chain_id'"`
	StoreId   string `json:"store_id" xorm:"not null comment('门店id') BIGINT 'store_id'"`
	ProductId int    `json:"product_id" xorm:"not null comment('商品id') BIGINT 'product_id'"`
	SkuId     int    `json:"sku_id" xorm:"not null comment('sku id') BIGINT 'sku_id'"`
}

func (c *ConsumerGroupHandler) ConsumeClaimTest(sess sarama.ConsumerGroupSession, claim sarama.ConsumerGroupClaim) error {

	//glog.Info("DoAllUserDetailKafka 接受到数据：" + string(msg.Value))
	//request := string(msg.Value)
	mqstr := []byte(`{"id":0,"database":"eshop_saas","table":"v_in_out_bound","pkNames":["id"],"isDdl":false,"type":"INSERT","es":1725260894000,"ts":1725260894505,"sql":"","sqlType":null,"mysqlType":{"bound_no":"varchar(32)","bound_type":"varchar(16)","chain_id":"bigint","created_by":"bigint","created_time":"datetime","id":"bigint","is_deleted":"bit(1)","item_ref_id":"bigint","item_ref_no":"varchar(50)","item_ref_type":"varchar(16)","item_type":"varchar(16)","occurrence_time":"datetime","operator":"varchar(255)","remark":"varchar(255)","sell_amount":"decimal(18,4)","tenant_id":"bigint","total_amount":"decimal(18,4)","total_num":"int","updated_by":"bigint","updated_time":"datetime"},"data":[{"bound_no":"CK2024090215081459851437","bound_type":"OUTBOUND","chain_id":"530185752454150978","store_id":"576534157590153975","warehouse_id":"3190","created_time":"2024-09-02 15:08:14","id":"1560","is_deleted":"0","item_ref_id":"534527526304048078","item_ref_no":"2024090215081335270674","item_ref_type":"ORDER","item_type":"ORDER_OUT","occurrence_time":"2024-09-02 15:08:14","operator":"老板","remark":"","sell_amount":"2.9800","tenant_id":"530219708465609002","total_amount":"1.5000","total_num":"2","updated_time":"2024-09-02 15:08:14"}],"old":null,"gtid":"9575c6cb-186f-11ef-9a00-b8cef6abb22c:374744659"}`)

	var mode = VInOutBound{}
	json.Unmarshal(mqstr, &mode)
	//fmt.Println(mode)
	//如果有数据
	db := GetDBConn()
	if len(mode.Data) > 0 && mode.Data[0].Id != "" {
		item := mode.Data[0]
		// 判断仓库id 查询表dc_dispatch.warehouse_relation_shop是不是渠道2，3的才进行后面的操作
		var channelExists bool
		channelExists, err := db.Table("dc_dispatch.warehouse_relation_shop").
			Where("shop_id = ? AND warehouse_id = ? AND channel_id IN (2,3)",
				item.StoreId,
				cast.ToInt(item.WarehouseId)).Exist()
		if err != nil {
			glog.Error("查询仓库渠道关系出错:", err.Error())
			return err
		}

		if !channelExists {
			glog.Info("该仓库未关联美团或饿了么渠道，跳过库存同步, warehouse_id:", item.WarehouseId)
			return nil
		}
		//查询出来要变更的库存

		var boundDetail = make([]VIoBoundDetail, 0)
		err = db.Table("eshop.inventory_in_out_bound_detail").Where("bound_id=? ", cast.ToInt(item.Id)).Find(&boundDetail)
		if err != nil {
			glog.Error("查询库存出错", err.Error())
		}
		if len(boundDetail) == 0 {
			//sess.MarkMessage(msg, "") // 标记消息已被处理
			return nil
		}

		var ids []int64
		for _, v := range boundDetail {
			ids = append(ids, cast.ToInt64(v.SkuId))
		}
		//查询出我们这边的skuid对应库存
		//查询出我们这边的skuid对应库存 .Select("/*FORCE_MASTER*/ *")
		var stocks = make([]VInventory, 0)
		err = db.Table("eshop.inventory").Alias("i").
			Join("INNER", "eshop.pro_sku p", "i.sku_id = p.id").
			Where("i.store_id = ?", cast.ToInt64(item.StoreId)).
			In("i.sku_id", ids).
			Select("p.product_id, p.id as sku_id, i.available_num").
			Find(&stocks)
		if err != nil {
			glog.Error(mode.Data[0].Id, "查询库存出错", err.Error())
			//sess.MarkMessage(msg, "") // 标记消息已被处理
			return err
		}
		glog.Info("库存写入MQ2：", mode.Data[0].Id, stocks)
		if len(stocks) == 0 {
			for i := 0; i < 5; i++ {
				time.Sleep(500 * time.Millisecond)
				err = db.Table("eshop.inventory").Alias("i").
					Join("INNER", "eshop.pro_sku p", "i.sku_id = p.id").
					Where("i.store_id = ?", cast.ToInt64(item.StoreId)).
					In("i.sku_id", ids).
					Select("p.product_id, p.id as sku_id, i.available_num").
					Find(&stocks)
				if len(stocks) > 0 {
					break
				}
			}
		}
		var skuIds []int64
		for _, v := range stocks {
			skuIds = append(skuIds, cast.ToInt64(v.SkuId))
		}
		var data = make([]models.ProProductStoreInfo, 0)
		//查询这些商品在对应的门店是否上架了
		err = db.Table("eshop.pro_product_store_info").
			Where("store_id=? and channel_id in (2,3) and up_down_state=1", item.StoreId).
			In("sku_id", skuIds).Find(&data)
		myMap := make(map[string]int)
		for _, v := range data {
			myMap[cast.ToString(v.SkuId)+":"+cast.ToString(v.ChannelId)] = 1
		}

		var mqInfos []models.MqInfo
		var foodDataMt []FoodData
		var foodDataELM []FoodData
		for _, v := range stocks {
			if _, exists := myMap[cast.ToString(v.SkuId)+":2"]; exists {
				//美团库存
				foodDataMt = append(foodDataMt, FoodData{
					App_food_code: cast.ToString(v.ProductId),
					Skus: []Skus{{
						Sku_id: cast.ToString(v.SkuId),
						Stock:  cast.ToString(v.AvailableNum),
					}},
				})
			}
			if _, exists := myMap[cast.ToString(v.SkuId)+":3"]; exists {
				//饿了么库存
				foodDataELM = append(foodDataELM, FoodData{
					App_food_code: cast.ToString(v.ProductId),
					Skus: []Skus{{
						Sku_id: cast.ToString(v.SkuId),
						Stock:  cast.ToString(v.AvailableNum),
					}},
				})
			}
		}

		if len(foodDataMt) > 0 {
			//美团MQ
			contentMt, _ := json.Marshal(RetailSkuStock{
				App_poi_code: item.StoreId,
				Food_data:    foodDataMt,
			})
			mqInfoMt := models.MqInfo{
				Exchange: "datacenter",
				Content:  string(contentMt),
				Quene:    "dc_sz_stock_mq",
			}
			mqInfos = append(mqInfos, mqInfoMt)
		}

		if len(foodDataELM) > 0 {
			//美团MQ
			contentMt, _ := json.Marshal(RetailSkuStock{
				App_poi_code: item.StoreId,
				Food_data:    foodDataELM,
			})
			mqInfoMt := models.MqInfo{
				Exchange: "datacenter",
				Content:  string(contentMt),
				Quene:    "dc_sz_stock_mq_elm",
			}
			mqInfos = append(mqInfos, mqInfoMt)
		}
		glog.Info("库存写入MQ：", foodDataMt, foodDataELM, mqInfos, stocks, myMap)

		if len(mqInfos) > 0 {
			if err = SliceInsertMqInfo(db, mqInfos); err != nil {
				glog.Error("SAAS同步库存到第三方MQ出错：", err.Error())
			}
		}

		//break
	}

	//sess.MarkMessage(msg, "") // 标记消息已被处理

	return nil
}
