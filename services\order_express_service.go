package services

import (
	"context"
	"crypto/rand"
	"encoding/json"
	"errors"
	"math/big"
	"order-center/proto/et"
	"order-center/proto/pay"
	"strings"
	"time"

	kit "github.com/tricobbler/rp-kit"

	"order-center/dto"
	"order-center/models"
	"order-center/proto/oc"
	"order-center/utils"

	"github.com/maybgit/glog"
	"github.com/spf13/cast"
)

// ExpressAddressDel 收件地址删除
func (OrderService) ExpressAddressDel(ctx context.Context, params *oc.ExpressAddressDelRequest) (out *oc.BaseResponse, err error) {
	return out, nil
}

// ExpressAddressList 获取快递地址列表
func (OrderService) ExpressAddressList(ctx context.Context, params *oc.ExpressAddressListRequest) (out *oc.ExprssAddressListResponse, err error) {
	return out, nil
}

// ExpressAddressSet 收件地址新增或修改
func (OrderService) ExpressAddressSet(ctx context.Context, params *oc.ExpressAddressSetRequest) (out *oc.BaseResponse, err error) {
	return out, nil
}

// ExpressCompanyList 获取物流公司列表
func (o OrderService) ExpressCompanyList(ctx context.Context, params *oc.ExpressCompanyListRequest) (out *oc.ExpressCompanyListResponse, err error) {
	glog.Info("获取物流公司列表:" + kit.JsonEncode(params))

	db := GetDBConn()

	out = new(oc.ExpressCompanyListResponse)
	out.DataList = make([]*oc.ExpressCompany, 0)
	companys := make([]*models.ExpressCompany, 0)
	err = db.Where("name like ?", "%"+params.Keyword+"%").Limit(int(params.PageSize), int((params.Page-1)*params.PageSize)).Find(&companys)
	if err != nil {
		glog.Error(params.Keyword + "获取物流公司列表" + err.Error())
		out.Code = 400
		out.Error = err.Error()
		return out, nil
	}

	for _, item := range companys {
		model := new(oc.ExpressCompany)
		model.Company = item.Name
		model.Id = item.Id
		out.DataList = append(out.DataList, model)
	}
	out.Code = 200
	out.Error = ""
	return out, nil
}

// 物流信息更新
func (o OrderService) ExpressInfoUpdate(ctx context.Context, params *oc.ExpressInfoUpdateRequest) (out *oc.BaseResponse, err error) {
	out = new(oc.BaseResponse)
	db := GetDBConn()
	if params.UpdateOrderType == 1 { //更新正向单物流信息
		glog.Info(params.OrderId + "订单物流信息更新:" + kit.JsonEncode(params))
		orderMain := GetOrderMainByOrderSn(params.OrderId)
		if orderMain.Id < 0 {
			out.Code = 400
			out.Message = "订单不存在"
			return
		}
		if orderMain.OrderStatus == 30 {
			out.Code = 400
			out.Message = "订单已完成，禁止更新物流信息"
			return
		}
		express := &models.UpetExpress{}
		_, e := GetUPetDBConn().Where("e_code_kdniao=?", params.ExpressCode).Get(express)
		if e != nil {
			out.Code = 400
			out.Message = "查询物流公司错误，" + e.Error()
			return
		}
		if express.Id < 1 {
			out.Code = 400
			out.Message = "物流公司不存在"
			return
		}
		params.ExpressNo = strings.Trim(params.ExpressNo, " ")
		if len(params.ExpressNo) == 0 {
			out.Code = 400
			out.Message = "物流单号错误"
			return
		}
		if _, e := db.Table("order_express").
			Where("id=?", params.OrderExpressId).
			Cols("express_no", "express_code", "express_name").
			Update(models.OrderExpress{
				ExpressNo:   params.ExpressNo,
				ExpressCode: params.ExpressCode,
				ExpressName: express.EName,
			}); e != nil {
			out.Code = 400
			out.Message = "更新物流信息失败"
			return
		}
		out.Code = 200
		return
	}

	glog.Info("退货物流信息更新:" + kit.JsonEncode(params))
	model := new(models.RefundOrderLogistics)
	has, err := db.Where("refund_sn=?", params.RefundSn).Get(model)
	if err != nil {
		glog.Error(params.OrderId + "退货物流信息更新" + err.Error())
		out.Code = 400
		out.Error = err.Error()
		return out, err
	}

	reOrder := models.RefundOrder{}
	hasReOrder, _ := db.Where("order_sn=? and refund_sn=?", params.OrderId, params.RefundSn).Get(&reOrder)
	if !hasReOrder {
		out.Code = 400
		out.Error = "售后单不存在"
		return out, err
	}

	if has {
		model.ExpressCompanyId = int(params.ExpressCompanyId)
		model.ExpressNo = params.ExpressNo
		model.UpdateTime = time.Now()

		_, err := db.Where("refund_sn=?", params.RefundSn).Update(model)
		if err != nil {
			glog.Error(params.RefundSn + "退货物流信息更新错误:" + err.Error())
			out.Code = 400
			out.Error = err.Error()
			return out, err
		}

	} else {
		model.ExpressCompanyId = int(params.ExpressCompanyId)
		model.ExpressNo = params.ExpressNo
		model.RefundSn = params.RefundSn
		model.CreateTime = time.Now()

		num, err := db.Insert(model)
		if err != nil {
			glog.Error(params.OrderId + "退货物流信息新增错误:" + err.Error())
			out.Code = 400
			out.Error = err.Error()
			return out, err
		}

		if num == 0 {
			glog.Error(params.RefundSn + "退货物流信息新增0条:")
			out.Code = 400
			out.Error = "退货物流信息新增0条"
			return out, err
		}

		if hasReOrder {
			reOrderLog := models.RefundOrderLog{
				RefundSn:   reOrder.RefundSn,
				OldOrderSn: reOrder.OldOrderSn,
				Ctime:      time.Now(),
				//Reason:        reOrder.RefundSn,
				Money:         cast.ToInt(reOrder.RefundAmount),
				OperationType: "用户填写退货物流单号信息",
				NotifyType:    "returnExpress",
				ResType:       "用户填写退货物流单号信息",
				Operationer:   params.ExpressInfo,
			}
			_, err = db.Insert(reOrderLog)
			if err != nil {
				glog.Error(params.OrderId + "退货物流信息插入退货日志异常:" + err.Error())
				out.Code = 400
				out.Error = "退货物流信息插入退货日志异常" + err.Error()
				return out, err
			}
		}
	}

	if hasReOrder {
		expressCompany := models.ExpressCompany{}
		db.Where("id=?", params.ExpressCompanyId).Get(&expressCompany)
		reOrder.ExpressName = expressCompany.Name
		reOrder.ExpressNum = params.ExpressNo
		db.Where("id=?", reOrder.Id).Update(reOrder)
	}

	out.Code = 200
	out.Error = ""
	return out, nil
}

// ExpressInfo 物流信息查询
func (o OrderService) ExpressInfo(ctx context.Context, params *oc.ExpressInfoRequest) (out *oc.ExpressInfoResponse, err error) {
	out = &oc.ExpressInfoResponse{Code: 200}
	db := GetDBConn()

	eCode := ""
	eExpressCompany := ""
	orderSn := ""
	mobile := ""

	if params.SearchType == "" {
		eCode = "HTKY"
		if params.ExpressNo == "" || params.RefundSn == "" {
			out.Code = 400
			out.Error = "物流单号或服务单Id不能为空"
			return out, nil
		}
		returnLogistisc := new(models.RefundOrderLogistics)
		out.ExpressNo = params.ExpressNo
		has, err := db.Where("express_no=? and refund_sn=?", params.ExpressNo, params.RefundSn).Get(returnLogistisc)
		if err != nil || !has {
			out.Error = "无对应退货信息"
			return out, nil
		}
		expressCompany := new(models.ExpressCompany)
		db.Where("id=?", returnLogistisc.ExpressCompanyId).Get(expressCompany)
		eExpressCompany = expressCompany.Name
		orderSn = returnLogistisc.RefundSn
	} else if params.SearchType == "1" {
		if params.ExpressNo == "" || params.OrderSn == "" {
			out.Code = 400
			out.Error = "物流单号或订单号不能为空"
			return out, nil
		}
		//目前巨益oms发货得到的信息没有手机号码，所以如果是顺丰快递，那么必然查不出快递信息
		orderExpress := &models.OrderExpress{}
		_, err := db.Where("order_sn=?", params.OrderSn).Where("express_no=?", params.ExpressNo).Get(orderExpress)
		if err != nil {
			out.Error = "暂无物流信息"
			return out, nil
		}
		eCode = orderExpress.ExpressCode
		eExpressCompany = orderExpress.ExpressName
		orderSn = params.OrderSn
	} else if params.SearchType == "2" {
		if params.ExpressNo == "" || params.OrderSn == "" {
			out.Code = 400
			out.Error = "物流单号或订单号不能为空"
			return out, nil
		}
		orderExpress := &models.OrderExpress{}
		var childOrderSn []string
		db.Table("order_main").Where("parent_order_sn=?", params.OrderSn).Cols("order_sn").Find(&childOrderSn)
		_, err := db.In("order_sn", childOrderSn).Where("express_no=?", params.ExpressNo).Get(orderExpress)
		if err != nil {
			out.Error = "暂无物流信息"
			return out, nil
		}
		eCode = orderExpress.ExpressCode
		eExpressCompany = orderExpress.ExpressName
		orderSn = params.OrderSn
	} else {
		out.Code = 400
		out.Error = "参数错误"
		out.Message = "参数错误"
		return out, nil
	}

	//如果是顺丰快递，需要手机号码查询
	if (params.SearchType == "1" || params.SearchType == "2") && (eCode == "SF" || eCode == "SFEXPRESS") {
		db.Table("order_main").Where("order_sn=?", params.OrderSn).Cols("receiver_mobile").Get(&mobile)
	}

	out.OrderNo = orderSn
	out.ExpressCompany = eExpressCompany
	out.ExpressNo = params.ExpressNo

	etClient := et.GetExternalClient()
	rs, err := etClient.Delivery.AliInfo(ctx, &et.DeliveryAliInfoRequest{
		Number: params.ExpressNo,
		Type:   eCode,
		Mobile: mobile,
	})
	if err != nil {
		out.Error = "暂无物流信息"
		out.Message = "暂无物流信息"
		return out, nil
	}

	for _, v := range rs.List {
		out.DataList = append(out.DataList, &oc.ExpressInfo{
			Datetime: v.Time,
			Info:     v.Desc,
			Status:   "",
		})
	}
	out.Error = ""
	return out, nil
}

// AwenOrderPay 订单支付
func (OrderService) AwenOrderPay(ctx context.Context, params *oc.AwenOrderPayRequest) (out *oc.BaseResponse, err error) {
	glog.Info("AwenOrderPay进入阿闻订单支付：", kit.JsonEncode(params))
	out = new(oc.BaseResponse)

	redisClient := GetRedisConn()
	lockCard := "order:orderpay:lock:" + params.OrderId
	lockRes := redisClient.SetNX(lockCard, time.Now().Unix(), 2*time.Second).Val()
	if !lockRes {
		out.Code = 400
		out.Error = "请勿频繁操作，稍后再试"
		return out, errors.New(out.Error)
	}

	key := "order:orderpay:trans" + cast.ToString(params.TransType) + ":"
	isOk := redisClient.Exists(key + params.OrderId)
	if isOk.Val() > 0 {
		codeStr := redisClient.Get(key + params.OrderId).Val() // 获取Redis中的Code
		out.Code = 200
		out.Message = codeStr
		return out, nil
	}

	order := GetOrderMainByOrderSn(params.OrderId, "*")
	if order.Id == 0 {
		out.Code = 400
		out.Error = "订单不存在"
		return out, nil
	}

	payForm := dto.AwenOrderPayForm{}
	payForm.TransType = params.TransType
	if params.TransType == 0 {
		payForm.TransType = 1
	}
	payForm.OutTradeNo = time.Now().Format("20060102150405") + RandomString(18, []rune("0123456789"))
	payForm.OrderId = params.OrderId
	payForm.PayPrice = order.Total

	payForm.ClientIP = params.ClientIp
	payForm.Discount = order.Privilege
	payForm.ExtendInfo = params.ExtendInfo
	payForm.OfflineNotifyUrl = utils.NotifyUrl // "https://**********:7040/order-api/ordercenter/order/offlinenotify"
	payForm.Openid = params.Openid

	payForm.TotalPrice = order.Privilege + order.Total
	payForm.ProductId = params.ProductId
	payForm.ProductName = params.ProductName
	payForm.ProductDesc = params.ProductDesc
	// 商户号切换   到店自提用新的微信商户号（利率：0.35%）,其他的 用旧的微信商户号（利率：0.95%），
	if order.DeliveryType == 3 { //到店自提 用新的微信商户号
		payForm.MerchantId = utils.MerchantId
	} else {
		payForm.MerchantId = "O-" + utils.MerchantId
	}
	payForm.SubAppId = utils.SubAppId

	//如果是saas平台的话，商户号什么的得改
	if order.AppChannel == 12 {
		db := GetDBConn()

		payinf := dto.PEpayConfig{}
		ishave, err := db.SQL("select * from eshop_saas.p_epay_config where tenant_id=? and app_type='WX_MINI'", cast.ToInt64(order.ShopId)).Get(&payinf)
		glog.Info("下单查询商户号:", order.ShopId, payinf)
		if err != nil {
			glog.Error("查询商户号出错:", order.ShopId, err.Error())
			out.Code = 400
			out.Error = err.Error()
			return out, nil
		}
		if !ishave {
			glog.Error("未开通小程序支付:", order.ShopId)
			out.Code = 400
			out.Error = "未开通小程序支付"
			return out, nil
		}

		payForm.MerchantId = payinf.AppMerchantId
		payForm.SubAppId = payinf.AppId
		params.AppId = 9
	}

	payForm.ClientIP = utils.GetClientIp()
	payForm.ExtendInfo = ""
	payForm.OrderPayType = order.OrderPayType
	payForm.AppId = params.AppId
	//默认5分钟有效期
	payForm.ValidTime = 5
	jsonForm := kit.JsonEncode(payForm)
	_, formData := utils.PayCenterSign(jsonForm)

	url := "/pay/unifiedorder" //"http://localhost:7035/pay/unifiedorder"
	glog.Info("阿闻订单支付请求参数：", utils.PayCenterUrl+url, formData)
	res, err := utils.HttpPost(utils.PayCenterUrl+url, []byte(formData), utils.ContentTypeToForm)
	glog.Info("阿闻订单支付结果：", string(res))

	//res, err := utils.HttpPost(url, []byte(formData), utils.ContentTypeToForm)
	if err != nil {
		glog.Error(url, "，支付中心接口调用失败，", err)
		out.Code = 400
		out.Error = err.Error()
		return out, nil
	}
	//glog.Info(url," result ",string(res))
	paycenterRes := dto.PayCenterResponse{}
	err = json.Unmarshal(res, &paycenterRes)
	if err != nil {

	}
	if paycenterRes.Code != 200 {
		out.Code = 200
		out.Error = paycenterRes.Message
		return out, nil
	}

	out.Code = 200
	out.Message = string(res)
	//一次支付会话过期时间是16分钟
	redisClient.Set(key+params.OrderId, string(res), 960*time.Second)
	return out, nil
}

// AwenOrderPayQuery 订单支付查询
func (OrderService) AwenOrderPayQuery(ctx context.Context, in *oc.AwenOrderPayQueryRequest) (out *oc.AwenOrderPayQueryResponse, err error) {
	out = new(oc.AwenOrderPayQueryResponse)
	pcc := pay.GetPayCenterClient()
	// 查询电银支付信息
	payInfo, err := pcc.RPC.PayInfoQuery(pcc.Ctx, &pay.PayInfoQueryRequest{OrderId: in.OrderId, MerchantId: utils.MerchantId})
	if err != nil {
		glog.Error("支付中心PayQueryByOrderId接口调用失败，", err)
		out.Code = 400
		out.Message = err.Error()
		return out, nil
	}
	if payInfo.Code != 200 {
		out.Code = 400
		out.Message = payInfo.Message
		return out, nil
	}

	// TransState S:交易成功 F:交易失败 P:交易处理中, status 0-交易中，1-交易完成，2-交易失败
	var status int32 = 3
	if payInfo.Data.TransState == "S" {
		status = 1
	} else if payInfo.Data.TransState == "P" {
		status = 0
	}

	out.Data = &oc.AwenOrderPayInfoData{
		OrderId:  payInfo.Data.OrderId,
		Status:   status,
		TradeNo:  payInfo.Data.TradeNo,
		PayPrice: payInfo.Data.TransAmt,
		AddTime:  payInfo.Data.OrderTime,
		PayTime:  payInfo.Data.PayTime,
	}
	out.Code = 200
	return
}

// 随机生成字符串
func RandomString(n int, allowedChars ...[]rune) string {
	var defaultLetters = []rune("abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789")
	var letters []rune

	if len(allowedChars) == 0 {
		letters = defaultLetters
	} else {
		letters = allowedChars[0]
	}

	b := make([]rune, n)
	for i := 0; i < n; i++ {
		n, _ := rand.Int(rand.Reader, big.NewInt(int64(len(letters))))
		//println(n.Int64())
		b[i] = letters[n.Int64()]
	}

	return string(b)
}

// AwenOrderPay 订单支付
func (o OrderService) AwenOrderB2CPay(ctx context.Context, params *oc.AwenOrderB2CPayRequest) (out *oc.BaseResponse, err error) {
	out = new(oc.BaseResponse)

	redisClient := GetRedisConn()
	//key := "order:orderpay:"
	//isOk := redisClient.Exists(key + params.OutOrderNo)
	//if isOk.Val() > 0 {
	//	codeStr := redisClient.Get(key + params.OutOrderNo).Val() // 获取Redis中的Code
	//	out.Code = 200
	//	return out, nil
	//}
	lockCard := "order:orderpay:lock:" + params.OutOrderNo
	lockRes := redisClient.SetNX(lockCard, time.Now().Unix(), 2*time.Second).Val()
	if !lockRes {
		out.Code = 400
		out.Error = "请勿频繁操作，稍后再试"
		return out, nil
	}
	payRequest := new(pay.PayForB2CRequest)
	payRequest.BarCode = params.BarCode
	//	out.Message = codeStr
	payRequest.OutOrderNo = params.OutOrderNo
	payRequest.PayType = params.PayType
	payRequest.TotalAmount = params.TotalAmount
	payRequest.PayAmount = params.PayAmount
	payRequest.Discount = params.Discount
	if params.Source == 1 {
		payRequest.OrderName = "阿闻商城-" + params.OutOrderNo
	} else {
		payRequest.OrderName = "阿闻到家-" + params.OutOrderNo
	}
	payRequest.NotifyUrl = utils.NotifyUrl
	//payRequest.NotifyUrl = "http://10.11.12.31:7040/order-api/order/offlinenotify"
	headBase := new(pay.ScanHeadBase)
	headBase.MercId = utils.MerchantId
	headBase.OrgId = params.OrgId
	headBase.TrmId = params.TrmId
	headBase.TrmSn = params.TrmSn
	payRequest.HeadBase = headBase
	payRequest.Location = params.Location

	conn := o.GetPayCenterClient()
	defer func() {
		if conn != nil {
			conn.Close()
		}
	}()
	client := pay.NewPayInfoClient(conn)
	res, err := client.PayForB2C(context.Background(), payRequest)
	if err != nil {
		glog.Error("调用支付中心B2C支付接口错误:"+kit.JsonEncode(payRequest), err.Error())
	}
	if res.Code != 200 {
		out.Code = 400
		out.Error = res.Message
		return out, nil
	}

	out.Code = 200
	out.Message = res.Message

	return out, nil
}

// OrderCancle 订单取消
func (o OrderService) AwenOrderCancle(ctx context.Context, params *oc.AwenOrderCancleRequest) (out *oc.BaseResponse, err error) {
	out = new(oc.BaseResponse)
	glog.Info("订单取消OrderId：" + params.OrderId + "，取消原因： " + params.CancelReason)

	o.orderMain = GetOrderMainByOrderSn(params.OrderId)
	if o.orderMain.Id == 0 {
		out.Code = 400
		out.Message = "未找到改订单"
		out.Error = out.Message
		return out, nil
	}

	o.session = GetDBConn().NewSession()
	defer o.session.Close()

	caseType := 0

	if o.orderMain.OrderStatus == 20 {
		caseType = 1
	}

	if o.orderMain.OrderStatus == 10 {
		if _, err := o.session.Exec("UPDATE `order_main` SET order_status=0,order_status_child=20107,cancel_reason='订单已取消',cancel_time=now() WHERE order_sn = '" + params.OrderId + "' AND order_status=10"); err != nil {
			glog.Error(err)
			out.Code = 400
			out.Error = "手动取消订单，更新订单状态异常:" + err.Error()
			out.Message = "手动取消订单，更新订单状态异常:" + err.Error()
			return out, nil
		}
		if o.orderMain.ParentOrderSn != "" && o.orderMain.AppChannel == SaasAppChannel {
			if _, err := o.session.Exec("UPDATE `order_main` SET order_status=0,order_status_child=20107,cancel_reason='订单已取消',cancel_time=now() WHERE order_sn = '" + o.orderMain.ParentOrderSn + "' AND order_status=10"); err != nil {
				glog.Error(err)
				out.Code = 400
				out.Error = "手动取消订单，更新主订单状态异常:" + err.Error()
				out.Message = "手动取消订单，更新主订单状态异常:" + err.Error()
				return out, nil
			}
		}

		// 释放库存
		err = o.FreedStock(nil)
		if err != nil {
			out.Error = "订单取消释放库存失败" + err.Error()
			out.Message = "订单取消释放库存失败"
			glog.Error("订单取消释放库存失败", params.OrderId, " ", err.Error())
			return out, nil
		}
		// 清除在途库存
		DeleteTransportationInventory(o.orderMain.OrderSn, o.orderMain.ChannelId)
	} else if !o.orderMain.DeliverTime.IsZero() {
		out.Code = 400
		out.Error = "订单已开始配送，不能取消"
		out.Message = "订单已开始配送，不能取消"
		return out, nil
	} else {
		var cancelRes *oc.BaseResponse

		cancelRes, err = o.CancelOrder(context.Background(), &oc.CancelOrderRequest{
			OrderSn:          params.OrderId,
			CancelReason:     "订单已取消",
			IsRefund:         0,
			OrderStatus:      20,                           //已付款
			OrderStatusChild: o.orderMain.OrderStatusChild, //未接单或已接单
		})

		if err != nil {
			glog.Error(o.orderMain.OrderSn, ", 用户取消订单失败, ", err)
			out.Code = 400
			out.Error = "手动取消订单，更新订单状态异常:" + err.Error()
			out.Message = "手动取消订单，更新订单状态异常:" + err.Error()
			return out, nil
		}

		if cancelRes.Code != 200 {
			glog.Error("用户取消订单失败！", o.orderMain.OrderSn, " ", kit.JsonEncode(cancelRes))
			out.Code = 400
			out.Error = cancelRes.Message
			out.Message = "取消订单失败!" + cancelRes.Message
			return out, nil
		}
		if o.orderMain.ChannelId == ChannelAwenId || o.orderMain.ChannelId == ChannelDigitalHealth {
			refundOrder := models.RefundOrder{}
			//查询日志
			isOk, err := o.session.SQL("select  * from `refund_order` where order_sn=? and `channel_id` in (1,9)", params.OrderId).Get(&refundOrder)
			if err != nil || !isOk {
				if err != nil {
					glog.Error("用户取消售后单调用退款支付接口查询不到状态订单:" + params.OrderId + err.Error())
				}
				out.Message = "用户取消售后单调用退款支付接口查询不到状态订单"
				out.Error = out.Message
				return out, nil
			}
			refundOrderPay := new(oc.RefundOrderPayRequest)
			refundOrderPay.RefundOrderSn = refundOrder.RefundSn
			refundOrderPay.ResType = "用户取消订单，自动发起退款申请"
			refundOrderPay.OperationType = "用户取消订单自动发起退款申请"
			refundOrderPay.OperationUser = o.orderMain.MemberName
			refundOrderService := RefundOrderService{}
			_, err = refundOrderService.RefundOrderPay(nil, refundOrderPay)
			if err != nil {
				glog.Error("商家取消订单自动发起退款申请返回错误："+refundOrder.RefundSn, err)
			}
		}
	}

	go func() {
		defer kit.CatchPanic()

		//释放阿闻到家小程序折扣活动当日库存
		o.FreedDailyStock()

		glog.Info("PushOrderStatusToTencent:" + params.OrderId + ":AwenOrderCancle")
		PushOrderStatusToTencent(params.OrderId, caseType)

		if o.orderMain.ChannelId == ChannelDigitalHealth || o.orderMain.ChannelId == ChannelAwenId {
			orderDetail := GetOrderDetailByOrderSn(o.orderMain.OrderSn)
			if orderDetail.ConsultOrderSn != "" {
				status := int32(4)
				if caseType == 1 {
					status = 3
				}
				_ = PushDigitalHealthOrder(o.orderMain.OrderSn, orderDetail.ConsultOrderSn, status, 0)
			}
		}
	}()

	out.Code = 200
	out.Error = ""
	out.Message = ""
	return out, nil
}
