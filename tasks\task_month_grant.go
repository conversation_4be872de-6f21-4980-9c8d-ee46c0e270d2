package tasks

import (
	"context"
	"errors"
	"fmt"
	"github.com/maybgit/glog"
	kit "github.com/tricobbler/rp-kit"
	"order-center/models"
	"order-center/proto/oc"
	"order-center/services"
	"strings"
	"time"
)

// VIP会员卡，每月需要发放的券
func TaskMonthGrant() {
	//连接池勿关闭
	redisConn := services.GetRedisConn()

	lockCard := "order-center:task:lock:month_grant"
	lockRes := redisConn.SetNX(lockCard, time.Now().Unix(), 30*time.Minute).Val()
	if !lockRes {
		return
	}
	defer redisConn.Del(lockCard)

	//找出需要发券的数据
	db := services.GetDcDBConn()
	//nowData := time.Now().Format(kit.DATE_LAYOUT)

	var ecs []*models.EquityConfigReceive
	//查询所有订单在有效期内，没有退款的，权益包含 商城优惠券，子龙门店券，子龙打折卡，并且是被动领取，并且是每月下发的,并且当月相同的权益在领取记录表里面是没有的
	//领券时间大于当前时间，或者
	//db.SQL("select e.id as equity_id,e.equity_type,ec.privilege_ids,ec.receive_num,e.receive_type,od.order_sn,od.user_id from "+
	//	"datacenter.vip_card_order od "+
	//	"inner join datacenter.vip_card_equity_config ec on od.card_id = od.card_id"+
	//	"inner join datacenter.vip_card_equity e on e.id = ec.equity_id "+
	//	"inner join datacenter.vip_user_equity_month em on em.equity_id = e.id and em.order_sn=od.order_sn "+
	//	"where e.equity_type in (2,8) and e.is_active=2 and e.issue_type=2 and od.state=10 and od.expiry_date<=? "+
	//	"and em.status=0 and em.begin_time>=? and end_time<?", nowData, nowData, nowData)
	if err := db.SQL(`SELECT e.id AS equity_id,e.equity_type,ec.privilege_ids,ec.receive_num,e.receive_type,od.order_sn,od.user_id,e.equity_short_name,ec.card_tid,ec.or_id,od.source  
		FROM 
		datacenter.vip_card_order od  
		INNER JOIN vip_card_template t ON od.card_id=t.id
		INNER JOIN datacenter.vip_card_equity_config ec ON t.id = ec.card_tid
		INNER JOIN datacenter.vip_card_equity e ON e.id = ec.equity_id 
		WHERE e.equity_type IN (1,2,8) AND e.is_active=2 AND e.issue_type=2 AND od.state=10 
		  AND find_in_set(od.collection_type,e.collection_ids) 
		  AND od.expiry_date>=NOW()
		  AND NOW()<=DATE_ADD(od.create_time,INTERVAL 360 DAY)
		AND NOT EXISTS  (
		   SELECT 1 FROM datacenter.vip_user_equity_record vc WHERE vc.order_sn=od.order_sn AND vc.equity_id=e.id
		   AND IF(NOW() <=DATE_ADD(od.create_time, INTERVAL ((DATE_FORMAT(NOW(), '%y')-DATE_FORMAT(od.create_time, '%y'))*12)+DATE_FORMAT(NOW(), '%m')-DATE_FORMAT(od.create_time, '%m') MONTH),
		        vc.create_time>=DATE_ADD(od.create_time, INTERVAL ((DATE_FORMAT(NOW(), '%y')-DATE_FORMAT(od.create_time, '%y'))*12)+DATE_FORMAT(NOW(), '%m')-DATE_FORMAT(od.create_time, '%m')-1 MONTH)
                   AND  vc.create_time<DATE_ADD(od.create_time, INTERVAL ((DATE_FORMAT(NOW(), '%y')-DATE_FORMAT(od.create_time, '%y'))*12)+DATE_FORMAT(NOW(), '%m')-DATE_FORMAT(od.create_time, '%m') MONTH),
		        vc.create_time>=DATE_ADD(od.create_time, INTERVAL ((DATE_FORMAT(NOW(), '%y')-DATE_FORMAT(od.create_time, '%y'))*12)+DATE_FORMAT(NOW(), '%m')-DATE_FORMAT(od.create_time, '%m') MONTH)
                   AND  vc.create_time<DATE_ADD(od.create_time, INTERVAL ((DATE_FORMAT(NOW(), '%y')-DATE_FORMAT(od.create_time, '%y'))*12)+DATE_FORMAT(NOW(), '%m')-DATE_FORMAT(od.create_time, '%m')+1 MONTH))
		)`).Find(&ecs); err != nil {
		return
	}
	glog.Info("month_task进入")
	//if err := db.Find(&ecs); err != nil {
	//	//out.Message = err.Error()
	//	return
	//}

	var c services.CardService
	for _, ec := range ecs {
		// 处理“医疗礼包”需要根据会员卡来源进行筛选，过滤掉不同销售方式的门店券
		if ec.EquityShortName == "医疗礼包" {
			privilegeIds, err := filterPrivilegeIds(ec)
			if err != nil {
				glog.Error(fmt.Sprintf("医疗礼包门店券自动发放，过滤门店券操作异常，e=%v", err.Error()))
				continue
			}
			ec.PrivilegeIds = privilegeIds
		}

		for i, id := range strings.Split(strings.TrimSpace(ec.PrivilegeIds), ",") {
			// 达到了领取限制，不处理
			if (i+1) > ec.ReceiveNum && (ec.ReceiveType == 1 || ec.ReceiveType == 2) {
				break
			}
			c.EquityReceive(context.Background(), &oc.CardEquityReceiveReq{
				OrderSn:    ec.OrderSn,
				EquityId:   ec.EquityId,
				Id:         id,
				ScrmId:     ec.UserId,
				IsMonth:    1,
				CreateTime: ec.CreateTime,
			})
		}
	}

}

// SalesTypeMap 卡来源对应销售方式 【卡来源：0-主动购买，1-分销购买，2-虚拟卡券兑换，3-门店开卡】 【销售方式：1-主动购买、分销购买、虚拟卡券兑换，2-充值赠送】
var SalesTypeMap = map[int]int{
	0: 1,
	1: 1,
	2: 1,
	3: 2,
}

func filterPrivilegeIds(ec *models.EquityConfigReceive) (string, error) {
	glog.Info(fmt.Sprintf("METHOD filterPrivilegeIds, params: ec = %v", kit.JsonEncode(ec)))
	privilegeIds := ec.PrivilegeIds
	source := ec.Source
	cardTid := ec.CardTid
	orId := ec.OrId
	equityId := ec.EquityId
	salesType := SalesTypeMap[source]

	session := services.GetDcDBConn().Table("vip_card_equity_value")
	var values []string

	glog.Info(fmt.Sprintf("METHOD filterPrivilegeIds, Sql args: card_tid=%v, or_id=%v, equity_id=%v, sales_type=%v", cardTid, orId, equityId, salesType))
	if err := session.SQL("SELECT privilege_id FROM datacenter.vip_card_equity_value WHERE card_tid=? AND or_id=? AND equity_id=? AND sales_type=?",
		cardTid, orId, equityId, salesType).Find(&values); err != nil {
		glog.Error(fmt.Sprintf("查询医疗礼包对应的赠送价值配置信息异常：card_tid=%v, or_id=%v, equity_id=%v, sales_type=%v", cardTid, orId, equityId, salesType))
		return "", errors.New("查询医疗礼包对应的赠送价值配置信息异常")
	}

	if len(values) > 0 {
		privilegeIds = strings.Join(values, ",")
	}

	return privilegeIds, nil
}
