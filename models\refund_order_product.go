package models

import (
	"order-center/proto/oc"
	"time"
)

type RefundOrderProduct struct {
	Id             int64     `xorm:"not null pk autoincr comment('自增主键') INT(11)"`
	RefundSn       string    `xorm:"default 'NULL' comment('退款单号') index VARCHAR(50)"`
	SkuId          string    `xorm:"not null default '''' comment('商品skuid') VARCHAR(20)"`
	OrderProductId int64     `xorm:"not null default 0 comment('订单商品表主键id') INT(11)"`
	ParentSkuId    string    `xorm:"not null default '''' comment('组合商品父级skuid') VARCHAR(50)"`
	ProductName    string    `xorm:"not null default '''' comment('商品名称') VARCHAR(200)"`
	ProductType    int32     `xorm:"not null default 1 comment('商品类别（1-实物商品，2-虚拟商品，3-组合商品）') TINYINT(4)"`
	ProductPrice   int32     `xorm:"not null default 0 comment('商品原优惠单价') INT(11)"`
	MarkingPrice   int32     `xorm:"not null default 0 comment('商品原单价') INT(11)"`
	Quantity       int32     `xorm:"not null default 0 comment('sku申请退款数量') INT(11)"`
	Tkcount        int32     `xorm:"not null default 0 comment('sku实际退款数量') INT(11)"`
	RefundAmount   string    `xorm:"not null default '''' comment('sku总退款金额') VARCHAR(20)"`
	RefundPrice    int32     `xorm:"not null default 0 comment('sku单件退款金额') INT(11)"`
	Itemcode       string    `xorm:"default 'NULL' comment('管易专用 商品代码') VARCHAR(50)"`
	Skucode        string    `xorm:"default 'NULL' comment('管易专用 带规格的商品此字段必填') VARCHAR(50)"`
	OcId           string    `xorm:"default 'NULL' comment('订单明细Id (全渠道需要)') VARCHAR(50)"`
	Barcode        string    `xorm:"default 'NULL' comment('管易专用 商品条码') VARCHAR(50)"`
	Spec           string    `xorm:"default 'NULL' comment('商品sku的规格名称') VARCHAR(200)"`
	BoxPrice       int32     `xorm:"default NULL comment('当前商品sku需使用包装盒的单价，即单个包装盒的价格，单位是元。') INT(11)"`
	BoxNum         float32   `xorm:"default NULL comment('单件商品sku需使用的包装盒数量，同商家同步商品时维护的此相同字段的信息。') FLOAT(10,2)"`
	SubBizOrderId  string    `xorm:"default 'NULL' comment('饿了么子订单ID，可区分同商品ID的不同属性，订单逆向操作必须字段') VARCHAR(200)"`
	VerifyCodes    string    `xorm:"not null default '''' comment('退款核销码，多个以英文逗号相隔') TEXT"`
	CreateTime     time.Time `xorm:" default 'current_timestamp()' comment('创建时间') DATETIME created"`
	UpdateTime     time.Time `xorm:" default 'current_timestamp()' comment('更新时间') DATETIME updated"`
}

type RefundOrderProductExt struct {
	RefundOrderProduct `xorm:"extends"`
	ProductId          string `json:"product_id"`
}

// 转换Dto
func (model *RefundOrderProduct) ToUpetDjRefundProductDto() *oc.UpetDjRefundProductDto {
	var dto = new(oc.UpetDjRefundProductDto)
	dto.ProductId = model.SkuId
	dto.RefundCount = model.Tkcount
	return dto
}

type RefundOrderProductFreight struct {
	*RefundOrderProduct `xorm:"extends"`
	Freight             string `xorm:"not null default '' comment('运费，单位元') VARCHAR(20)"`
}
